# البدء السريع - نظام إدارة مركز صيانة السيارات

## 🚀 تشغيل النظام في 5 دقائق

### الخطوة 1: التحضير (دقيقة واحدة)
```bash
# تأكد من تثبيت Node.js
node --version  # يجب أن يكون 18 أو أحدث

# إذا لم يكن مثبت، حمله من nodejs.org
```

### الخطوة 2: تثبيت المتطلبات (دقيقتان)
```bash
# في مجلد المشروع
npm install
```

### الخطوة 3: إعداد قاعدة البيانات (دقيقتان)

#### أ. إنشاء مشروع Supabase
1. اذهب إلى [supabase.com](https://supabase.com)
2. سجل دخول أو أنشئ حساب
3. اضغط "New Project"
4. اختر اسم وكلمة مرور للمشروع

#### ب. إعد<PERSON> الجداول
1. اذهب إلى "SQL Editor"
2. انسخ والصق محتوى `database/schema.sql`
3. اضغط "Run"

#### ج. الحصول على المفاتيح
1. اذهب إلى "Settings" > "API"
2. انسخ "Project URL" و "anon public key"

### الخطوة 4: إعداد البيئة (30 ثانية)
```bash
# انسخ ملف الإعدادات
cp .env.local.example .env.local

# عدل الملف وأضف بيانات Supabase
```

محتوى `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

### الخطوة 5: تشغيل النظام (30 ثانية)
```bash
npm run dev
```

## 🎉 مبروك! النظام جاهز

افتح المتصفح واذهب إلى: **http://localhost:3000**

### بيانات تجريبية للدخول:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** 123456

## 📱 جولة سريعة في النظام

### 1. الصفحة الرئيسية (`/`)
- مقدمة عن النظام
- عرض الميزات الرئيسية
- رابط تسجيل الدخول

### 2. تسجيل الدخول (`/login`)
- استخدم البيانات التجريبية أعلاه
- واجهة جميلة ومتجاوبة

### 3. لوحة التحكم (`/dashboard`)
- إحصائيات سريعة
- إجراءات سريعة
- نظرة عامة على الأداء

### 4. إدارة العملاء (`/customers`)
- قائمة العملاء مع البحث
- إضافة عميل جديد
- تعديل بيانات العملاء

### 5. إدارة السيارات (`/vehicles`)
- قائمة السيارات
- ربط السيارات بالعملاء
- تفاصيل كل سيارة

### 6. إدارة الصيانة (`/maintenance`)
- طلبات الصيانة
- تتبع الحالة
- تعيين الفنيين

### 7. إدارة المخزون (`/inventory`)
- قطع الغيار
- تنبيهات المخزون المنخفض
- إدارة الموردين

### 8. التقارير (`/reports`)
- إحصائيات شاملة
- مخططات بيانية
- تقارير قابلة للتصدير

### 9. الإعدادات (`/settings`)
- إعدادات المركز
- إدارة المستخدمين
- النسخ الاحتياطي

## 🔧 اختبار الميزات

### إضافة عميل جديد:
1. اذهب إلى `/customers`
2. اضغط "عميل جديد"
3. املأ البيانات واحفظ

### إضافة سيارة:
1. اذهب إلى `/vehicles`
2. اضغط "سيارة جديدة"
3. اختر العميل واملأ بيانات السيارة

### إنشاء طلب صيانة:
1. اذهب إلى `/maintenance`
2. اضغط "طلب صيانة جديد"
3. اختر العميل والسيارة
4. اكتب وصف المشكلة

### عرض الإشعارات:
1. اضغط على أيقونة الجرس في الأعلى
2. ستظهر الإشعارات التجريبية

## 📊 البيانات التجريبية المتاحة

النظام يأتي مع بيانات تجريبية:

### العملاء:
- أحمد محمد علي
- فاطمة أحمد  
- محمد عبدالله

### الفنيين:
- خالد السعد (ميكانيكا عامة)
- عبدالرحمن أحمد (كهرباء السيارات)
- سعد محمد (تكييف وتبريد)

### الخدمات:
- تغيير زيت المحرك
- فحص شامل
- إصلاح الفرامل
- تغيير إطارات

### قطع الغيار:
- زيت محرك 5W-30
- فلتر زيت
- فلتر هواء
- تيل فرامل أمامي
- إطار 205/55R16

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

### 1. تخصيص البيانات
- عدل معلومات المركز في الإعدادات
- أضف فنيين حقيقيين
- أضف خدمات وقطع غيار خاصة بمركزك

### 2. إضافة بيانات حقيقية
- ابدأ بإضافة عملاء حقيقيين
- سجل سياراتهم
- أنشئ طلبات صيانة فعلية

### 3. استكشاف الميزات
- جرب جميع الصفحات
- اختبر البحث والتصفية
- استخدم نظام الإشعارات

## ⚠️ مشاكل شائعة وحلول سريعة

### المشكلة: النظام لا يعمل
**الحل السريع:**
```bash
# أعد تشغيل النظام
npm run dev
```

### المشكلة: البيانات لا تظهر
**الحل:**
1. تحقق من `.env.local`
2. تأكد من تشغيل `schema.sql` في Supabase

### المشكلة: خطأ في المنفذ 3000
**الحل:**
```bash
npm run dev -- -p 3001
```

## 📞 الدعم

إذا واجهت مشاكل:
1. راجع `TROUBLESHOOTING.md`
2. راجع `INSTALLATION.md` للتفاصيل
3. تحقق من `README.md`

## 🎊 مبروك!

أنت الآن تملك نظام إدارة مركز صيانة سيارات متكامل وجاهز للاستخدام!

### الميزات المتاحة:
✅ إدارة العملاء والسيارات  
✅ تتبع طلبات الصيانة  
✅ إدارة المخزون والقطع  
✅ نظام الفواتير  
✅ التقارير والإحصائيات  
✅ نظام الإشعارات  
✅ واجهة عربية كاملة  
✅ تصميم متجاوب  

**استمتع باستخدام النظام! 🚗💨**
