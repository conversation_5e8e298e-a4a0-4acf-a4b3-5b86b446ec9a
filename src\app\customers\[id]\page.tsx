'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Users, 
  Edit, 
  Phone, 
  Mail, 
  MapPin, 
  Car, 
  Wrench, 
  FileText,
  Calendar,
  Plus
} from 'lucide-react'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import { DatabaseService } from '@/lib/supabase'
import type { Customer, Vehicle, ServiceRequest } from '@/types/database'

export default function CustomerDetailsPage() {
  const params = useParams()
  const customerId = params.id as string
  
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'vehicles' | 'services' | 'invoices'>('vehicles')

  useEffect(() => {
    if (customerId) {
      loadCustomerData()
    }
  }, [customerId])

  const loadCustomerData = async () => {
    try {
      const [customerData, vehiclesData] = await Promise.all([
        DatabaseService.getCustomerById(customerId),
        DatabaseService.getVehiclesByCustomer(customerId)
      ])
      
      setCustomer(customerData)
      setVehicles(vehiclesData || [])
      
      // Load service requests for this customer
      // This would be implemented in DatabaseService
      setServiceRequests([])
    } catch (error) {
      console.error('Error loading customer data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل بيانات العميل...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!customer) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            العميل غير موجود
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            لم يتم العثور على العميل المطلوب
          </p>
          <div className="mt-6">
            <Link
              href="/customers"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة لقائمة العملاء
            </Link>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const tabs = [
    { id: 'vehicles', name: 'السيارات', count: vehicles.length },
    { id: 'services', name: 'طلبات الصيانة', count: serviceRequests.length },
    { id: 'invoices', name: 'الفواتير', count: 0 }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              href="/customers"
              className="ml-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <ArrowLeft className="h-6 w-6" />
            </Link>
            <Users className="h-8 w-8 text-blue-600 ml-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {customer.name}
              </h1>
              <p className="text-sm text-gray-500">
                عميل منذ {new Date(customer.created_at).toLocaleDateString('ar-SA')}
              </p>
            </div>
          </div>
          <Link
            href={`/customers/${customer.id}/edit`}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <Edit className="h-4 w-4 ml-2" />
            تعديل البيانات
          </Link>
        </div>

        {/* Customer Info Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            معلومات العميل
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center">
              <Phone className="h-5 w-5 text-gray-400 ml-3" />
              <div>
                <p className="text-sm text-gray-500">رقم الهاتف</p>
                <p className="text-sm font-medium text-gray-900">{customer.phone}</p>
              </div>
            </div>
            
            {customer.email && (
              <div className="flex items-center">
                <Mail className="h-5 w-5 text-gray-400 ml-3" />
                <div>
                  <p className="text-sm text-gray-500">البريد الإلكتروني</p>
                  <p className="text-sm font-medium text-gray-900">{customer.email}</p>
                </div>
              </div>
            )}
            
            {customer.address && (
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-gray-400 ml-3" />
                <div>
                  <p className="text-sm text-gray-500">العنوان</p>
                  <p className="text-sm font-medium text-gray-900">{customer.address}</p>
                </div>
              </div>
            )}
            
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-gray-400 ml-3" />
              <div>
                <p className="text-sm text-gray-500">تاريخ التسجيل</p>
                <p className="text-sm font-medium text-gray-900">
                  {new Date(customer.created_at).toLocaleDateString('ar-SA')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 space-x-reverse px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                  {tab.count > 0 && (
                    <span className={`mr-2 py-0.5 px-2 rounded-full text-xs ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* Vehicles Tab */}
            {activeTab === 'vehicles' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    سيارات العميل ({vehicles.length})
                  </h3>
                  <Link
                    href={`/vehicles/new?customer=${customer.id}`}
                    className="btn-primary flex items-center"
                  >
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة سيارة
                  </Link>
                </div>
                
                {vehicles.length === 0 ? (
                  <div className="text-center py-8">
                    <Car className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      لا توجد سيارات مسجلة
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      ابدأ بإضافة سيارة لهذا العميل
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {vehicles.map((vehicle) => (
                      <div
                        key={vehicle.id}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center">
                            <Car className="h-5 w-5 text-green-600 ml-2" />
                            <h4 className="font-medium text-gray-900">
                              {vehicle.make} {vehicle.model}
                            </h4>
                          </div>
                          <span className="text-sm text-gray-500">{vehicle.year}</span>
                        </div>
                        <div className="space-y-1 text-sm text-gray-600">
                          <p><strong>رقم اللوحة:</strong> {vehicle.license_plate}</p>
                          {vehicle.color && <p><strong>اللون:</strong> {vehicle.color}</p>}
                          {vehicle.mileage && <p><strong>العداد:</strong> {vehicle.mileage.toLocaleString()} كم</p>}
                        </div>
                        <div className="mt-3 flex justify-end">
                          <Link
                            href={`/vehicles/${vehicle.id}`}
                            className="text-green-600 hover:text-green-700 text-sm font-medium"
                          >
                            عرض التفاصيل
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Services Tab */}
            {activeTab === 'services' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    طلبات الصيانة ({serviceRequests.length})
                  </h3>
                  <Link
                    href={`/maintenance/new?customer=${customer.id}`}
                    className="btn-primary flex items-center"
                  >
                    <Plus className="h-4 w-4 ml-2" />
                    طلب صيانة جديد
                  </Link>
                </div>
                
                <div className="text-center py-8">
                  <Wrench className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    لا توجد طلبات صيانة
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    لم يتم تسجيل أي طلبات صيانة لهذا العميل بعد
                  </p>
                </div>
              </div>
            )}

            {/* Invoices Tab */}
            {activeTab === 'invoices' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    الفواتير (0)
                  </h3>
                  <Link
                    href={`/invoices/new?customer=${customer.id}`}
                    className="btn-primary flex items-center"
                  >
                    <Plus className="h-4 w-4 ml-2" />
                    فاتورة جديدة
                  </Link>
                </div>
                
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    لا توجد فواتير
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    لم يتم إصدار أي فواتير لهذا العميل بعد
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
