import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database helper functions
export class DatabaseService {
  // Customers
  static async getCustomers() {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  static async getCustomerById(id: string) {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  }

  static async createCustomer(customer: any) {
    const { data, error } = await supabase
      .from('customers')
      .insert(customer)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  static async updateCustomer(id: string, updates: any) {
    const { data, error } = await supabase
      .from('customers')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  static async deleteCustomer(id: string) {
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }

  // Vehicles
  static async getVehicles() {
    const { data, error } = await supabase
      .from('vehicles')
      .select(`
        *,
        customer:customers(*)
      `)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  static async getVehiclesByCustomer(customerId: string) {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  static async createVehicle(vehicle: any) {
    const { data, error } = await supabase
      .from('vehicles')
      .insert(vehicle)
      .select()
      .single()

    if (error) throw error
    return data
  }

  static async updateVehicle(id: string, updates: any) {
    const { data, error } = await supabase
      .from('vehicles')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  static async deleteVehicle(id: string) {
    const { error } = await supabase
      .from('vehicles')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Service Requests
  static async getServiceRequests() {
    const { data, error } = await supabase
      .from('service_requests')
      .select(`
        *,
        customer:customers(*),
        vehicle:vehicles(*),
        technician:technicians(*)
      `)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  }

  static async createServiceRequest(serviceRequest: any) {
    const { data, error } = await supabase
      .from('service_requests')
      .insert(serviceRequest)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  static async updateServiceRequestStatus(id: string, status: string) {
    const { data, error } = await supabase
      .from('service_requests')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // Technicians
  static async getTechnicians() {
    const { data, error } = await supabase
      .from('technicians')
      .select('*')
      .eq('is_active', true)
      .order('name')
    
    if (error) throw error
    return data
  }

  // Parts and Inventory
  static async getParts() {
    const { data, error } = await supabase
      .from('parts')
      .select('*')
      .eq('is_active', true)
      .order('name')
    
    if (error) throw error
    return data
  }

  static async getInventory() {
    const { data, error } = await supabase
      .from('inventory')
      .select(`
        *,
        part:parts(*)
      `)
      .order('last_updated', { ascending: false })
    
    if (error) throw error
    return data
  }

  static async getLowStockItems() {
    const { data, error } = await supabase
      .from('inventory')
      .select(`
        *,
        part:parts(*)
      `)
      .filter('quantity', 'lte', 'min_quantity')
    
    if (error) throw error
    return data
  }

  // Dashboard Statistics
  static async getDashboardStats() {
    try {
      const [
        customersCount,
        vehiclesCount,
        pendingServices,
        completedToday,
        lowStockCount
      ] = await Promise.all([
        supabase.from('customers').select('id', { count: 'exact' }),
        supabase.from('vehicles').select('id', { count: 'exact' }),
        supabase.from('service_requests').select('id', { count: 'exact' }).eq('status', 'pending'),
        supabase.from('service_requests').select('id', { count: 'exact' })
          .eq('status', 'completed')
          .gte('completion_date', new Date().toISOString().split('T')[0]),
        supabase.from('inventory').select('id', { count: 'exact' })
          .filter('quantity', 'lte', 'min_quantity')
      ])

      return {
        total_customers: customersCount.count || 0,
        total_vehicles: vehiclesCount.count || 0,
        pending_services: pendingServices.count || 0,
        completed_services_today: completedToday.count || 0,
        total_revenue_month: 0, // Will be calculated from invoices
        low_stock_items: lowStockCount.count || 0
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      return {
        total_customers: 0,
        total_vehicles: 0,
        pending_services: 0,
        completed_services_today: 0,
        total_revenue_month: 0,
        low_stock_items: 0
      }
    }
  }
}
