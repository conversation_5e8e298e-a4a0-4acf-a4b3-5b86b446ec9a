'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Users, Save, X } from 'lucide-react'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import { DatabaseService } from '@/lib/supabase'
import type { Customer, CustomerFormData } from '@/types/database'

export default function EditCustomerPage() {
  const params = useParams()
  const router = useRouter()
  const customerId = params.id as string
  
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    phone: '',
    email: '',
    address: ''
  })
  const [errors, setErrors] = useState<Partial<CustomerFormData>>({})

  useEffect(() => {
    if (customerId) {
      loadCustomer()
    }
  }, [customerId])

  const loadCustomer = async () => {
    try {
      const customerData = await DatabaseService.getCustomerById(customerId)
      setCustomer(customerData)
      setFormData({
        name: customerData.name,
        phone: customerData.phone,
        email: customerData.email || '',
        address: customerData.address || ''
      })
    } catch (error) {
      console.error('Error loading customer:', error)
    } finally {
      setLoading(false)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<CustomerFormData> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'اسم العميل مطلوب'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب'
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح'
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setSaving(true)
    try {
      const updateData = {
        ...formData,
        email: formData.email || null,
        address: formData.address || null,
        updated_at: new Date().toISOString()
      }

      await DatabaseService.updateCustomer(customerId, updateData)
      router.push(`/customers/${customerId}`)
    } catch (error) {
      console.error('Error updating customer:', error)
      alert('حدث خطأ أثناء تحديث بيانات العميل')
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل بيانات العميل...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!customer) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            العميل غير موجود
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            لم يتم العثور على العميل المطلوب
          </p>
          <div className="mt-6">
            <Link
              href="/customers"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة لقائمة العملاء
            </Link>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center">
          <Link
            href={`/customers/${customerId}`}
            className="ml-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-6 w-6" />
          </Link>
          <Users className="h-8 w-8 text-blue-600 ml-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              تعديل بيانات العميل
            </h1>
            <p className="text-sm text-gray-500">
              {customer.name}
            </p>
          </div>
        </div>

        <div className="max-w-2xl">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Customer Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  اسم العميل *
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`input-field ${errors.name ? 'border-red-500' : ''}`}
                  placeholder="أدخل اسم العميل"
                  required
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              {/* Phone Number */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الهاتف *
                </label>
                <input
                  type="tel"
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`input-field ${errors.phone ? 'border-red-500' : ''}`}
                  placeholder="أدخل رقم الهاتف"
                  required
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`input-field ${errors.email ? 'border-red-500' : ''}`}
                  placeholder="أدخل البريد الإلكتروني (اختياري)"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* Address */}
              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                  العنوان
                </label>
                <textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={3}
                  className="input-field resize-none"
                  placeholder="أدخل عنوان العميل (اختياري)"
                />
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
                <Link
                  href={`/customers/${customerId}`}
                  className="btn-secondary flex items-center"
                >
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Link>
                <button
                  type="submit"
                  disabled={saving}
                  className="btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 ml-2" />
                      حفظ التغييرات
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Customer Info */}
          <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-800 mb-2">
              معلومات إضافية:
            </h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>تاريخ التسجيل:</strong> {new Date(customer.created_at).toLocaleDateString('ar-SA')}</p>
              <p><strong>آخر تحديث:</strong> {new Date(customer.updated_at).toLocaleDateString('ar-SA')}</p>
              <p><strong>رقم العميل:</strong> {customer.id.slice(0, 8)}</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
