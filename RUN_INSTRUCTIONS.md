# 🚀 تعليمات تشغيل نظام إدارة مركز صيانة السيارات

## 📋 قائمة التحقق السريعة

قبل البدء، تأكد من توفر:
- [ ] Node.js 18 أو أحدث
- [ ] حساب Supabase (مجاني)
- [ ] محرر نصوص (VS Code مُوصى به)
- [ ] متصفح حديث

## 🎯 خطوات التشغيل المفصلة

### الخطوة 1: إعداد البيئة المحلية

#### تثبيت Node.js:
1. اذهب إلى [nodejs.org](https://nodejs.org/)
2. حمل النسخة LTS (الموصى بها)
3. ثب<PERSON> البرنامج واتبع التعليمات
4. تحقق من التثبيت:
```bash
node --version  # يجب أن يظهر v18.x.x أو أحدث
npm --version   # يجب أن يظهر 9.x.x أو أحدث
```

### الخطوة 2: إعداد المشروع

#### تحميل الملفات:
```bash
# إذا كان لديك Git
git clone [repository-url]
cd car-center-management

# أو قم بتحميل الملفات وفك الضغط في مجلد
```

#### تثبيت المتطلبات:
```bash
npm install
```

**ملاحظة:** قد يستغرق هذا 2-3 دقائق حسب سرعة الإنترنت.

### الخطوة 3: إعداد قاعدة البيانات (Supabase)

#### إنشاء مشروع جديد:
1. اذهب إلى [supabase.com](https://supabase.com)
2. اضغط "Start your project"
3. سجل دخول أو أنشئ حساب جديد
4. اضغط "New Project"
5. اختر:
   - **اسم المشروع:** car-center-db
   - **كلمة مرور قاعدة البيانات:** اختر كلمة مرور قوية
   - **المنطقة:** اختر الأقرب لك
6. اضغط "Create new project"

#### إعداد الجداول:
1. انتظر حتى يكتمل إنشاء المشروع (2-3 دقائق)
2. اذهب إلى "SQL Editor" من الشريط الجانبي
3. افتح ملف `database/schema.sql` من مجلد المشروع
4. انسخ المحتوى كاملاً
5. الصق في SQL Editor
6. اضغط "Run" أو Ctrl+Enter
7. يجب أن ترى رسالة "Success"

#### الحصول على مفاتيح API:
1. اذهب إلى "Settings" > "API"
2. انسخ:
   - **Project URL** (مثل: https://abc123.supabase.co)
   - **anon public key** (مفتاح طويل يبدأ بـ eyJ...)

### الخطوة 4: إعداد متغيرات البيئة

#### إنشاء ملف الإعدادات:
```bash
# في مجلد المشروع
cp .env.local.example .env.local
```

#### تعديل الملف:
افتح `.env.local` في محرر النصوص وعدل:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

**مهم:** استبدل القيم بالقيم الحقيقية من Supabase.

### الخطوة 5: تشغيل النظام

#### تشغيل الخادم:
```bash
npm run dev
```

#### التحقق من التشغيل:
- يجب أن ترى رسالة: "Ready - started server on 0.0.0.0:3000"
- افتح المتصفح واذهب إلى: http://localhost:3000
- يجب أن تظهر الصفحة الرئيسية للنظام

## 🎉 تسجيل الدخول والاختبار

### بيانات الدخول التجريبية:
- **الرابط:** http://localhost:3000/login
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** 123456

### اختبار الميزات:
1. **لوحة التحكم:** http://localhost:3000/dashboard
2. **إدارة العملاء:** http://localhost:3000/customers
3. **إدارة السيارات:** http://localhost:3000/vehicles
4. **إدارة الصيانة:** http://localhost:3000/maintenance
5. **إدارة المخزون:** http://localhost:3000/inventory
6. **التقارير:** http://localhost:3000/reports
7. **الإعدادات:** http://localhost:3000/settings

## 🔍 التحقق من البيانات التجريبية

### في Supabase:
1. اذهب إلى "Table Editor"
2. تحقق من الجداول التالية:
   - **customers:** يجب أن يحتوي على 3 عملاء
   - **technicians:** يجب أن يحتوي على 3 فنيين
   - **services:** يجب أن يحتوي على 4 خدمات
   - **parts:** يجب أن يحتوي على 5 قطع غيار
   - **inventory:** يجب أن يحتوي على 5 عناصر مخزون

### في النظام:
- اذهب إلى صفحة العملاء - يجب أن ترى 3 عملاء
- اذهب إلى صفحة المخزون - يجب أن ترى 5 قطع غيار
- جرب إضافة عميل جديد للتأكد من عمل النظام

## ⚠️ حل المشاكل الشائعة

### المشكلة: "Module not found"
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

### المشكلة: "Port 3000 is already in use"
```bash
# استخدم منفذ مختلف
npm run dev -- -p 3001
```

### المشكلة: البيانات لا تظهر
1. تحقق من `.env.local`
2. تأكد من تشغيل `schema.sql` في Supabase
3. تحقق من Console في المتصفح (F12)

### المشكلة: خطأ في Supabase
1. تأكد من صحة URL و API Key
2. تحقق من حالة مشروع Supabase
3. تأكد من تشغيل جميع الاستعلامات في SQL Editor

## 📱 استخدام النظام

### إضافة عميل جديد:
1. اذهب إلى "إدارة العملاء"
2. اضغط "عميل جديد"
3. املأ البيانات المطلوبة
4. احفظ

### إضافة سيارة:
1. اذهب إلى "إدارة السيارات"
2. اضغط "سيارة جديدة"
3. اختر العميل
4. املأ بيانات السيارة
5. احفظ

### إنشاء طلب صيانة:
1. اذهب إلى "إدارة الصيانة"
2. اضغط "طلب صيانة جديد"
3. اختر العميل والسيارة
4. اكتب وصف المشكلة
5. احفظ

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

### تخصيص النظام:
1. عدل معلومات المركز في الإعدادات
2. أضف فنيين حقيقيين
3. أضف خدمات وقطع غيار خاصة بمركزك

### إضافة بيانات حقيقية:
1. ابدأ بإضافة عملاء حقيقيين
2. سجل سياراتهم
3. أنشئ طلبات صيانة فعلية

### النسخ الاحتياطي:
1. فعل النسخ الاحتياطي في Supabase
2. احفظ نسخة من ملف `.env.local`

## 📞 الحصول على المساعدة

إذا واجهت مشاكل:
1. راجع `TROUBLESHOOTING.md`
2. راجع `INSTALLATION.md`
3. تحقق من `README.md`
4. راجع `FEATURES.md` للميزات المتاحة

## 🎊 مبروك!

أنت الآن تملك نظام إدارة مركز صيانة سيارات متكامل وجاهز للاستخدام!

### ما حققته:
✅ نظام إدارة شامل  
✅ قاعدة بيانات متكاملة  
✅ واجهة عربية جميلة  
✅ تقارير وإحصائيات  
✅ نظام إشعارات  
✅ تصميم متجاوب  

**استمتع باستخدام النظام وإدارة مركز الصيانة بكفاءة! 🚗💨**
