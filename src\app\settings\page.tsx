'use client'

import { useState } from 'react'
import { 
  Settings, 
  Users, 
  Bell, 
  Database, 
  Shield, 
  Palette,
  Save,
  Download,
  Upload,
  Trash2
} from 'lucide-react'
import DashboardLayout from '@/components/Layout/DashboardLayout'

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<'general' | 'users' | 'notifications' | 'backup' | 'security'>('general')
  const [loading, setLoading] = useState(false)

  const tabs = [
    { id: 'general', name: 'الإعدادات العامة', icon: Settings },
    { id: 'users', name: 'إدارة المستخدمين', icon: Users },
    { id: 'notifications', name: 'الإشعارات', icon: Bell },
    { id: 'backup', name: 'النسخ الاحتياطي', icon: Database },
    { id: 'security', name: 'الأمان', icon: Shield }
  ]

  const handleSave = async () => {
    setLoading(true)
    try {
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000))
      alert('تم حفظ الإعدادات بنجاح')
    } catch (error) {
      alert('حدث خطأ أثناء حفظ الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Settings className="h-8 w-8 text-gray-600 ml-3" />
            <h1 className="text-2xl font-bold text-gray-900">
              إعدادات النظام
            </h1>
          </div>
          <button
            onClick={handleSave}
            disabled={loading}
            className="btn-primary flex items-center disabled:opacity-50"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                جاري الحفظ...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 ml-2" />
                حفظ الإعدادات
              </>
            )}
          </button>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 space-x-reverse px-6 overflow-x-auto">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4 ml-2" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>

          <div className="p-6">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    معلومات المركز
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم المركز
                      </label>
                      <input
                        type="text"
                        defaultValue="مركز الخليج لصيانة السيارات"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف
                      </label>
                      <input
                        type="tel"
                        defaultValue="0112345678"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني
                      </label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الموقع الإلكتروني
                      </label>
                      <input
                        type="url"
                        defaultValue="www.carservice.com"
                        className="input-field"
                      />
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      العنوان
                    </label>
                    <textarea
                      rows={3}
                      defaultValue="الرياض، حي الملك فهد، شارع الملك عبدالعزيز"
                      className="input-field resize-none"
                    />
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    إعدادات العملة والضرائب
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العملة الافتراضية
                      </label>
                      <select className="input-field">
                        <option value="SAR">ريال سعودي (SAR)</option>
                        <option value="USD">دولار أمريكي (USD)</option>
                        <option value="EUR">يورو (EUR)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نسبة الضريبة (%)
                      </label>
                      <input
                        type="number"
                        defaultValue="15"
                        min="0"
                        max="100"
                        step="0.01"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تطبيق الضريبة
                      </label>
                      <select className="input-field">
                        <option value="inclusive">شامل الضريبة</option>
                        <option value="exclusive">بدون ضريبة</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Users Management */}
            {activeTab === 'users' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    المستخدمون
                  </h3>
                  <button className="btn-primary flex items-center">
                    <Users className="h-4 w-4 ml-2" />
                    إضافة مستخدم
                  </button>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="table-header">الاسم</th>
                        <th className="table-header">البريد الإلكتروني</th>
                        <th className="table-header">الدور</th>
                        <th className="table-header">الحالة</th>
                        <th className="table-header">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="table-cell">أحمد محمد</td>
                        <td className="table-cell"><EMAIL></td>
                        <td className="table-cell">
                          <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            مدير النظام
                          </span>
                        </td>
                        <td className="table-cell">
                          <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                            نشط
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="flex space-x-2 space-x-reverse">
                            <button className="text-blue-600 hover:text-blue-900">
                              تعديل
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              حذف
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Notifications */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">
                  إعدادات الإشعارات
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">إشعارات البريد الإلكتروني</h4>
                      <p className="text-sm text-gray-500">إرسال إشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">إشعارات SMS</h4>
                      <p className="text-sm text-gray-500">إرسال رسائل نصية للعملاء</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">تذكير الصيانة الدورية</h4>
                      <p className="text-sm text-gray-500">تذكير العملاء بمواعيد الصيانة</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Backup */}
            {activeTab === 'backup' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">
                  النسخ الاحتياطي واستعادة البيانات
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border border-gray-200 rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-4">إنشاء نسخة احتياطية</h4>
                    <p className="text-sm text-gray-600 mb-4">
                      قم بإنشاء نسخة احتياطية من جميع بيانات النظام
                    </p>
                    <button className="btn-primary flex items-center w-full justify-center">
                      <Download className="h-4 w-4 ml-2" />
                      تحميل النسخة الاحتياطية
                    </button>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-4">استعادة البيانات</h4>
                    <p className="text-sm text-gray-600 mb-4">
                      استعادة البيانات من نسخة احتياطية سابقة
                    </p>
                    <button className="btn-secondary flex items-center w-full justify-center">
                      <Upload className="h-4 w-4 ml-2" />
                      رفع النسخة الاحتياطية
                    </button>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-800 mb-2">تنبيه مهم</h4>
                  <p className="text-sm text-yellow-700">
                    يُنصح بإنشاء نسخة احتياطية بشكل دوري لضمان عدم فقدان البيانات. 
                    آخر نسخة احتياطية تم إنشاؤها في: 15 يناير 2024
                  </p>
                </div>
              </div>
            )}

            {/* Security */}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">
                  إعدادات الأمان
                </h3>

                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-4">كلمة المرور</h4>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          كلمة المرور الحالية
                        </label>
                        <input type="password" className="input-field" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          كلمة المرور الجديدة
                        </label>
                        <input type="password" className="input-field" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          تأكيد كلمة المرور الجديدة
                        </label>
                        <input type="password" className="input-field" />
                      </div>
                      <button className="btn-primary">
                        تحديث كلمة المرور
                      </button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-4">إعدادات الجلسة</h4>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          مدة انتهاء الجلسة (بالدقائق)
                        </label>
                        <input 
                          type="number" 
                          defaultValue="60" 
                          min="5" 
                          max="480" 
                          className="input-field" 
                        />
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="checkbox" 
                          id="force-logout" 
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" 
                        />
                        <label htmlFor="force-logout" className="mr-2 block text-sm text-gray-900">
                          إنهاء جميع الجلسات النشطة عند تغيير كلمة المرور
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
