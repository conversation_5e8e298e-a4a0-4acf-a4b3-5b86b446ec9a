'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Car, 
  Edit, 
  User, 
  Calendar, 
  Gauge, 
  Wrench, 
  FileText,
  Plus,
  Settings
} from 'lucide-react'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import { DatabaseService } from '@/lib/supabase'
import type { Vehicle, ServiceRequest } from '@/types/database'

export default function VehicleDetailsPage() {
  const params = useParams()
  const vehicleId = params.id as string
  
  const [vehicle, setVehicle] = useState<Vehicle | null>(null)
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'info' | 'services' | 'maintenance'>('info')

  useEffect(() => {
    if (vehicleId) {
      loadVehicleData()
    }
  }, [vehicleId])

  const loadVehicleData = async () => {
    try {
      // This would be implemented in DatabaseService
      // const vehicleData = await DatabaseService.getVehicleById(vehicleId)
      // setVehicle(vehicleData)
      
      // Mock data for now
      const mockVehicle: Vehicle = {
        id: vehicleId,
        customer_id: '1',
        make: 'تويوتا',
        model: 'كامري',
        year: 2020,
        license_plate: 'أ ب ج 1234',
        chassis_number: 'JTDKN3DU5L5123456',
        color: 'أبيض',
        mileage: 45000,
        created_at: '2023-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        customer: {
          id: '1',
          name: 'أحمد محمد علي',
          phone: '**********',
          email: '<EMAIL>',
          address: 'الرياض، حي النخيل',
          created_at: '2023-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        }
      }
      
      setVehicle(mockVehicle)
      setServiceRequests([])
    } catch (error) {
      console.error('Error loading vehicle data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل بيانات السيارة...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!vehicle) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Car className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            السيارة غير موجودة
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            لم يتم العثور على السيارة المطلوبة
          </p>
          <div className="mt-6">
            <Link
              href="/vehicles"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة لقائمة السيارات
            </Link>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const tabs = [
    { id: 'info', name: 'معلومات السيارة', icon: Car },
    { id: 'services', name: 'تاريخ الصيانة', icon: Wrench },
    { id: 'maintenance', name: 'جدولة الصيانة', icon: Calendar }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              href="/vehicles"
              className="ml-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <ArrowLeft className="h-6 w-6" />
            </Link>
            <Car className="h-8 w-8 text-green-600 ml-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {vehicle.make} {vehicle.model} {vehicle.year}
              </h1>
              <p className="text-sm text-gray-500">
                {vehicle.license_plate}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Link
              href={`/vehicles/${vehicle.id}/edit`}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
            >
              <Edit className="h-4 w-4 ml-2" />
              تعديل البيانات
            </Link>
            <Link
              href={`/maintenance/new?vehicle=${vehicle.id}`}
              className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
            >
              <Plus className="h-4 w-4 ml-2" />
              طلب صيانة
            </Link>
          </div>
        </div>

        {/* Vehicle Overview Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center">
              <User className="h-5 w-5 text-gray-400 ml-3" />
              <div>
                <p className="text-sm text-gray-500">المالك</p>
                <Link 
                  href={`/customers/${vehicle.customer?.id}`}
                  className="text-sm font-medium text-blue-600 hover:text-blue-700"
                >
                  {vehicle.customer?.name}
                </Link>
              </div>
            </div>
            
            <div className="flex items-center">
              <Settings className="h-5 w-5 text-gray-400 ml-3" />
              <div>
                <p className="text-sm text-gray-500">رقم الشاسيه</p>
                <p className="text-sm font-medium text-gray-900 font-mono">
                  {vehicle.chassis_number}
                </p>
              </div>
            </div>
            
            {vehicle.color && (
              <div className="flex items-center">
                <div 
                  className="w-5 h-5 rounded-full border border-gray-300 ml-3"
                  style={{ backgroundColor: vehicle.color === 'أبيض' ? '#ffffff' : vehicle.color }}
                ></div>
                <div>
                  <p className="text-sm text-gray-500">اللون</p>
                  <p className="text-sm font-medium text-gray-900">{vehicle.color}</p>
                </div>
              </div>
            )}
            
            {vehicle.mileage && (
              <div className="flex items-center">
                <Gauge className="h-5 w-5 text-gray-400 ml-3" />
                <div>
                  <p className="text-sm text-gray-500">قراءة العداد</p>
                  <p className="text-sm font-medium text-gray-900">
                    {vehicle.mileage.toLocaleString()} كم
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 space-x-reverse px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                      activeTab === tab.id
                        ? 'border-green-500 text-green-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4 ml-2" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>

          <div className="p-6">
            {/* Vehicle Info Tab */}
            {activeTab === 'info' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    تفاصيل السيارة
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">الماركة</label>
                        <p className="mt-1 text-sm text-gray-900">{vehicle.make}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">الموديل</label>
                        <p className="mt-1 text-sm text-gray-900">{vehicle.model}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">سنة الصنع</label>
                        <p className="mt-1 text-sm text-gray-900">{vehicle.year}</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">رقم اللوحة</label>
                        <p className="mt-1 text-sm text-gray-900 font-mono">{vehicle.license_plate}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">تاريخ التسجيل</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {new Date(vehicle.created_at).toLocaleDateString('ar-SA')}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">آخر تحديث</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {new Date(vehicle.updated_at).toLocaleDateString('ar-SA')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Owner Info */}
                {vehicle.customer && (
                  <div className="border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      معلومات المالك
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{vehicle.customer.name}</h4>
                          <p className="text-sm text-gray-600">{vehicle.customer.phone}</p>
                          {vehicle.customer.email && (
                            <p className="text-sm text-gray-600">{vehicle.customer.email}</p>
                          )}
                        </div>
                        <Link
                          href={`/customers/${vehicle.customer.id}`}
                          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                          عرض ملف العميل
                        </Link>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Services Tab */}
            {activeTab === 'services' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    تاريخ الصيانة
                  </h3>
                  <Link
                    href={`/maintenance/new?vehicle=${vehicle.id}`}
                    className="btn-primary flex items-center"
                  >
                    <Plus className="h-4 w-4 ml-2" />
                    طلب صيانة جديد
                  </Link>
                </div>
                
                <div className="text-center py-8">
                  <Wrench className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    لا يوجد تاريخ صيانة
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    لم يتم تسجيل أي عمليات صيانة لهذه السيارة بعد
                  </p>
                </div>
              </div>
            )}

            {/* Maintenance Tab */}
            {activeTab === 'maintenance' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  جدولة الصيانة الدورية
                </h3>
                
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    لا توجد مواعيد مجدولة
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    يمكنك جدولة مواعيد الصيانة الدورية للسيارة
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
