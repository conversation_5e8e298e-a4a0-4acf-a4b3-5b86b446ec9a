'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Wrench, 
  Edit, 
  User, 
  Car, 
  Calendar, 
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Plus,
  Package,
  DollarSign
} from 'lucide-react'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import { DatabaseService } from '@/lib/supabase'
import type { ServiceRequest, ServiceItem } from '@/types/database'

export default function MaintenanceDetailsPage() {
  const params = useParams()
  const requestId = params.id as string
  
  const [serviceRequest, setServiceRequest] = useState<ServiceRequest | null>(null)
  const [serviceItems, setServiceItems] = useState<ServiceItem[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'details' | 'items' | 'history'>('details')

  useEffect(() => {
    if (requestId) {
      loadServiceRequestData()
    }
  }, [requestId])

  const loadServiceRequestData = async () => {
    try {
      // Mock data for now - in real app, this would come from API
      const mockServiceRequest: ServiceRequest = {
        id: requestId,
        customer_id: '1',
        vehicle_id: '1',
        technician_id: '1',
        request_date: '2024-01-15T10:00:00Z',
        scheduled_date: '2024-01-16T09:00:00Z',
        completion_date: null,
        status: 'in_progress',
        priority: 'medium',
        description: 'تغيير زيت المحرك وفحص شامل للسيارة',
        notes: 'العميل يشكو من صوت غريب في المحرك',
        total_cost: 350.00,
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        customer: {
          id: '1',
          name: 'أحمد محمد علي',
          phone: '**********',
          email: '<EMAIL>',
          address: 'الرياض، حي النخيل',
          created_at: '2023-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        vehicle: {
          id: '1',
          customer_id: '1',
          make: 'تويوتا',
          model: 'كامري',
          year: 2020,
          license_plate: 'أ ب ج 1234',
          chassis_number: 'JTDKN3DU5L5123456',
          color: 'أبيض',
          mileage: 45000,
          created_at: '2023-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        technician: {
          id: '1',
          name: 'خالد السعد',
          phone: '**********',
          email: '<EMAIL>',
          specialization: 'ميكانيكا عامة',
          hire_date: '2023-01-15',
          is_active: true,
          created_at: '2023-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        }
      }
      
      const mockServiceItems: ServiceItem[] = [
        {
          id: '1',
          service_request_id: requestId,
          service_id: '1',
          part_id: null,
          quantity: 1,
          unit_price: 150.00,
          total_price: 150.00,
          description: 'تغيير زيت المحرك',
          service: {
            id: '1',
            name: 'تغيير زيت المحرك',
            description: 'تغيير زيت المحرك والفلتر',
            base_price: 150.00,
            estimated_duration: 30,
            category: 'maintenance',
            is_active: true,
            created_at: '2023-01-15T10:00:00Z',
            updated_at: '2024-01-15T10:00:00Z'
          }
        },
        {
          id: '2',
          service_request_id: requestId,
          service_id: null,
          part_id: '1',
          quantity: 4,
          unit_price: 25.00,
          total_price: 100.00,
          description: 'زيت محرك 5W-30',
          part: {
            id: '1',
            name: 'زيت محرك 5W-30',
            part_number: 'OIL-5W30-4L',
            description: 'زيت محرك عالي الجودة',
            unit_price: 25.00,
            supplier: 'شركة الزيوت المتقدمة',
            category: 'زيوت ومواد تشحيم',
            is_active: true,
            created_at: '2023-01-15T10:00:00Z',
            updated_at: '2024-01-15T10:00:00Z'
          }
        }
      ]
      
      setServiceRequest(mockServiceRequest)
      setServiceItems(mockServiceItems)
    } catch (error) {
      console.error('Error loading service request data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'in_progress':
        return <AlertCircle className="h-5 w-5 text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'in_progress':
        return 'قيد التنفيذ'
      case 'completed':
        return 'مكتملة'
      case 'cancelled':
        return 'ملغية'
      default:
        return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل تفاصيل طلب الصيانة...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!serviceRequest) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Wrench className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            طلب الصيانة غير موجود
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            لم يتم العثور على طلب الصيانة المطلوب
          </p>
          <div className="mt-6">
            <Link
              href="/maintenance"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700"
            >
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة لقائمة طلبات الصيانة
            </Link>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const tabs = [
    { id: 'details', name: 'تفاصيل الطلب' },
    { id: 'items', name: 'الخدمات والقطع' },
    { id: 'history', name: 'سجل التحديثات' }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              href="/maintenance"
              className="ml-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <ArrowLeft className="h-6 w-6" />
            </Link>
            <Wrench className="h-8 w-8 text-orange-600 ml-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                طلب صيانة #{serviceRequest.id.slice(0, 8)}
              </h1>
              <p className="text-sm text-gray-500">
                {serviceRequest.description}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Link
              href={`/maintenance/${serviceRequest.id}/edit`}
              className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
            >
              <Edit className="h-4 w-4 ml-2" />
              تعديل الطلب
            </Link>
          </div>
        </div>

        {/* Status and Priority */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className={`w-3 h-3 rounded-full ${getPriorityColor(serviceRequest.priority)}`}></div>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(serviceRequest.status)}`}>
                {getStatusIcon(serviceRequest.status)}
                <span className="mr-2">{getStatusText(serviceRequest.status)}</span>
              </span>
              <span className="text-sm text-gray-500">
                أولوية: {serviceRequest.priority === 'urgent' ? 'عاجلة' : 
                         serviceRequest.priority === 'high' ? 'عالية' :
                         serviceRequest.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
              </span>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">التكلفة الإجمالية</p>
              <p className="text-2xl font-bold text-gray-900">
                {serviceRequest.total_cost?.toFixed(2)} ر.س
              </p>
            </div>
          </div>
        </div>

        {/* Quick Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <User className="h-8 w-8 text-blue-600 ml-3" />
              <div>
                <p className="text-sm text-gray-500">العميل</p>
                <Link 
                  href={`/customers/${serviceRequest.customer?.id}`}
                  className="text-lg font-medium text-blue-600 hover:text-blue-700"
                >
                  {serviceRequest.customer?.name}
                </Link>
                <p className="text-sm text-gray-500">{serviceRequest.customer?.phone}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Car className="h-8 w-8 text-green-600 ml-3" />
              <div>
                <p className="text-sm text-gray-500">السيارة</p>
                <Link 
                  href={`/vehicles/${serviceRequest.vehicle?.id}`}
                  className="text-lg font-medium text-green-600 hover:text-green-700"
                >
                  {serviceRequest.vehicle?.make} {serviceRequest.vehicle?.model}
                </Link>
                <p className="text-sm text-gray-500">{serviceRequest.vehicle?.license_plate}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Wrench className="h-8 w-8 text-purple-600 ml-3" />
              <div>
                <p className="text-sm text-gray-500">الفني المسؤول</p>
                <p className="text-lg font-medium text-gray-900">
                  {serviceRequest.technician?.name || 'لم يتم التعيين'}
                </p>
                {serviceRequest.technician && (
                  <p className="text-sm text-gray-500">{serviceRequest.technician.specialization}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 space-x-reverse px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* Details Tab */}
            {activeTab === 'details' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات الطلب</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">تاريخ الطلب</label>
                        <p className="text-sm text-gray-900">
                          {new Date(serviceRequest.request_date).toLocaleDateString('ar-SA')}
                        </p>
                      </div>
                      {serviceRequest.scheduled_date && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700">الموعد المجدول</label>
                          <p className="text-sm text-gray-900">
                            {new Date(serviceRequest.scheduled_date).toLocaleString('ar-SA')}
                          </p>
                        </div>
                      )}
                      {serviceRequest.completion_date && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700">تاريخ الإكمال</label>
                          <p className="text-sm text-gray-900">
                            {new Date(serviceRequest.completion_date).toLocaleString('ar-SA')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">وصف المشكلة</h3>
                    <p className="text-sm text-gray-700 bg-gray-50 p-4 rounded-lg">
                      {serviceRequest.description}
                    </p>
                    {serviceRequest.notes && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</h4>
                        <p className="text-sm text-gray-700 bg-yellow-50 p-4 rounded-lg">
                          {serviceRequest.notes}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Items Tab */}
            {activeTab === 'items' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">
                    الخدمات وقطع الغيار
                  </h3>
                  <button className="btn-primary flex items-center">
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة عنصر
                  </button>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="table-header">النوع</th>
                        <th className="table-header">الوصف</th>
                        <th className="table-header">الكمية</th>
                        <th className="table-header">سعر الوحدة</th>
                        <th className="table-header">الإجمالي</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {serviceItems.map((item) => (
                        <tr key={item.id}>
                          <td className="table-cell">
                            {item.service ? (
                              <div className="flex items-center">
                                <Wrench className="h-4 w-4 text-blue-500 ml-2" />
                                <span className="text-sm text-blue-600">خدمة</span>
                              </div>
                            ) : (
                              <div className="flex items-center">
                                <Package className="h-4 w-4 text-green-500 ml-2" />
                                <span className="text-sm text-green-600">قطعة غيار</span>
                              </div>
                            )}
                          </td>
                          <td className="table-cell">
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {item.description}
                              </p>
                              {item.part && (
                                <p className="text-xs text-gray-500">
                                  رقم القطعة: {item.part.part_number}
                                </p>
                              )}
                            </div>
                          </td>
                          <td className="table-cell">
                            <span className="text-sm text-gray-900">{item.quantity}</span>
                          </td>
                          <td className="table-cell">
                            <span className="text-sm text-gray-900">
                              {item.unit_price.toFixed(2)} ر.س
                            </span>
                          </td>
                          <td className="table-cell">
                            <span className="text-sm font-medium text-gray-900">
                              {item.total_price.toFixed(2)} ر.س
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-gray-50">
                      <tr>
                        <td colSpan={4} className="table-cell text-right font-medium">
                          الإجمالي:
                        </td>
                        <td className="table-cell">
                          <span className="text-lg font-bold text-gray-900">
                            {serviceItems.reduce((sum, item) => sum + item.total_price, 0).toFixed(2)} ر.س
                          </span>
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            )}

            {/* History Tab */}
            {activeTab === 'history' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  سجل التحديثات
                </h3>
                
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    لا يوجد سجل تحديثات
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    سيتم عرض جميع التحديثات والتغييرات هنا
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
