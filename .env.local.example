# Supabase Configuration
# احصل على هذه القيم من لوحة تحكم Supabase > Settings > API
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# اختياري - للعمليات الإدارية المتقدمة
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# إعدادات التطبيق
NEXT_PUBLIC_APP_NAME="مركز الخليج لصيانة السيارات"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# إعدادات البيئة
NODE_ENV=development
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# SMS Configuration (Optional)
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=your_sender_id
