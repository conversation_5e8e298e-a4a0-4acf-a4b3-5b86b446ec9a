# دليل حل المشاكل - نظام إدارة مركز صيانة السيارات

## المشاكل الشائعة وحلولها

### 1. مشاكل التثبيت والإعداد

#### مشكلة: خطأ في تثبيت Node.js
**الأعراض:**
```
'node' is not recognized as an internal or external command
```

**الحل:**
1. تأكد من تثبيت Node.js من [nodejs.org](https://nodejs.org/)
2. أعد تشغيل Command Prompt/Terminal
3. تأكد من إضافة Node.js إلى PATH

#### مشكلة: خطأ في تثبيت المتطلبات
**الأعراض:**
```
npm ERR! code ENOENT
npm ERR! syscall open
```

**الحل:**
```bash
# امسح الملفات المؤقتة
rm -rf node_modules
rm package-lock.json

# أعد التثبيت
npm cache clean --force
npm install
```

### 2. مشاكل قاعدة البيانات

#### مشكلة: خطأ في الاتصال بـ Supabase
**الأعراض:**
- الصفحات لا تحمل البيانات
- رسائل خطأ في Console

**الحل:**
1. تحقق من ملف `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

2. تأكد من صحة الروابط والمفاتيح
3. تحقق من حالة مشروع Supabase

#### مشكلة: الجداول غير موجودة
**الأعراض:**
```
relation "customers" does not exist
```

**الحل:**
1. اذهب إلى Supabase SQL Editor
2. شغل ملف `database/schema.sql` كاملاً
3. تأكد من إنشاء جميع الجداول

#### مشكلة: البيانات التجريبية مفقودة
**الحل:**
```sql
-- شغل هذا الاستعلام في Supabase SQL Editor
INSERT INTO customers (name, phone, email, address) VALUES
('أحمد محمد علي', '0501234567', '<EMAIL>', 'الرياض، حي النخيل'),
('فاطمة أحمد', '0509876543', '<EMAIL>', 'جدة، حي الصفا'),
('محمد عبدالله', '0551234567', NULL, 'الدمام، حي الفيصلية');
```

### 3. مشاكل واجهة المستخدم

#### مشكلة: الصفحات تظهر باللغة الإنجليزية
**الحل:**
1. تأكد من إعدادات المتصفح للغة العربية
2. امسح cache المتصفح
3. أعد تحميل الصفحة

#### مشكلة: التصميم مكسور أو غير منسق
**الحل:**
```bash
# أعد تشغيل النظام
npm run dev
```

#### مشكلة: الأيقونات لا تظهر
**الحل:**
```bash
# تأكد من تثبيت lucide-react
npm install lucide-react
```

### 4. مشاكل الأداء

#### مشكلة: النظام بطيء في التحميل
**الحل:**
1. تحقق من سرعة الإنترنت
2. تأكد من موقع خادم Supabase
3. استخدم وضع الإنتاج:
```bash
npm run build
npm start
```

#### مشكلة: استهلاك عالي للذاكرة
**الحل:**
```bash
# أعد تشغيل النظام
npm run dev
```

### 5. مشاكل المتصفح

#### مشكلة: النظام لا يعمل في Internet Explorer
**الحل:**
استخدم متصفح حديث مثل:
- Google Chrome
- Mozilla Firefox
- Microsoft Edge
- Safari

#### مشكلة: مشاكل في عرض الخطوط العربية
**الحل:**
1. تأكد من دعم المتصفح للخطوط العربية
2. امسح cache المتصفح
3. تحقق من إعدادات الخط في النظام

### 6. مشاكل تسجيل الدخول

#### مشكلة: لا يمكن تسجيل الدخول
**الحل:**
استخدم البيانات التجريبية:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** 123456

#### مشكلة: نسيان كلمة المرور
**الحل:**
حالياً النظام يستخدم مصادقة تجريبية. استخدم البيانات أعلاه.

### 7. مشاكل الإشعارات

#### مشكلة: الإشعارات لا تظهر
**الحل:**
1. تأكد من تفعيل الإشعارات في المتصفح
2. امسح cache المتصفح
3. أعد تحميل الصفحة

### 8. مشاكل التقارير

#### مشكلة: المخططات البيانية لا تظهر
**الحل:**
```bash
# تأكد من تثبيت recharts
npm install recharts
```

### 9. مشاكل الطباعة والتصدير

#### مشكلة: لا يمكن تصدير التقارير
**الحل:**
هذه الميزة قيد التطوير. ستكون متاحة في التحديثات القادمة.

## أدوات التشخيص

### 1. فحص Console المتصفح
```javascript
// افتح Developer Tools (F12) وشغل:
console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
```

### 2. فحص حالة قاعدة البيانات
```sql
-- شغل في Supabase SQL Editor
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public';
```

### 3. فحص الاتصال
```bash
# تحقق من حالة الخادم
curl http://localhost:3000
```

## رسائل الخطأ الشائعة

### خطأ: "Module not found"
**الحل:**
```bash
npm install
```

### خطأ: "Port 3000 is already in use"
**الحل:**
```bash
# استخدم منفذ مختلف
npm run dev -- -p 3001
```

### خطأ: "Invalid API key"
**الحل:**
تحقق من مفاتيح Supabase في `.env.local`

### خطأ: "Network Error"
**الحل:**
1. تحقق من الاتصال بالإنترنت
2. تحقق من حالة خوادم Supabase

## نصائح للأداء الأمثل

### 1. تحسين قاعدة البيانات
- استخدم الفهارس للبحث السريع
- نظف البيانات القديمة دورياً

### 2. تحسين المتصفح
- استخدم متصفح حديث
- فعل JavaScript
- امسح Cache دورياً

### 3. تحسين الشبكة
- استخدم اتصال إنترنت مستقر
- اختر موقع Supabase الأقرب

## طلب المساعدة

إذا لم تجد حل لمشكلتك:

### 1. جمع المعلومات
- نسخة Node.js: `node --version`
- نسخة npm: `npm --version`
- نظام التشغيل
- المتصفح المستخدم
- رسالة الخطأ الكاملة

### 2. التحقق من الملفات
- تأكد من وجود جميع الملفات
- تحقق من صحة `.env.local`
- راجع `package.json`

### 3. إعادة التثبيت
```bash
# إعادة تثبيت كاملة
rm -rf node_modules
rm package-lock.json
npm install
npm run dev
```

### 4. التواصل للدعم
- راجع ملف `README.md`
- راجع ملف `FEATURES.md`
- افتح Issue في GitHub

---

**نصيحة:** احتفظ بنسخة احتياطية من قاعدة البيانات دورياً لتجنب فقدان البيانات.
