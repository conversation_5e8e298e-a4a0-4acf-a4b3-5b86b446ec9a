'use client'

import { useState } from 'react'
import { Bell, Menu, Search, User } from 'lucide-react'
import NotificationCenter from '../Notifications/NotificationCenter'

interface HeaderProps {
  onMenuClick: () => void
}

export default function Header({ onMenuClick }: HeaderProps) {
  const [notificationsOpen, setNotificationsOpen] = useState(false)

  return (
    <>
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-4 py-4">
          {/* Left side - Menu button and search */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={onMenuClick}
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <Menu className="h-6 w-6" />
            </button>

            <div className="hidden md:block">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="البحث..."
                  className="pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                />
              </div>
            </div>
          </div>

          {/* Right side - Notifications and user menu */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Notifications */}
            <button
              onClick={() => setNotificationsOpen(true)}
              className="relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
            >
              <Bell className="h-6 w-6" />
              <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
            </button>

          {/* User menu */}
          <div className="relative">
            <button className="flex items-center space-x-2 space-x-reverse p-2 text-gray-700 hover:bg-gray-100 rounded-lg">
              <div className="bg-blue-100 p-2 rounded-full">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              <div className="hidden md:block text-right">
                <p className="text-sm font-medium">أحمد محمد</p>
                <p className="text-xs text-gray-500">مدير النظام</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </header>

    {/* Notification Center */}
    <NotificationCenter
      isOpen={notificationsOpen}
      onClose={() => setNotificationsOpen(false)}
    />
    </>
  )
}
