# نظام إدارة مركز صيانة السيارات

نظام شامل لإدارة مركز صيانة السيارات مبني بتقنية Next.js و TypeScript مع قاعدة بيانات Supabase.

## الميزات الرئيسية

### 1. إدارة العملاء
- تسجيل بيانات العملاء (الاسم، رقم الهاتف، العنوان، البريد الإلكتروني)
- البحث عن العملاء وتعديل بياناتهم
- عرض تاريخ تعاملات العميل مع المركز

### 2. إدارة السيارات
- تسجيل بيانات السيارات (الماركة، الموديل، سنة الصنع، رقم اللوحة، رقم الشاسيه)
- ربط السيارات بأصحابها
- تسجيل قراءة العداد في كل زيارة

### 3. إدارة الصيانة
- تسجيل طلبات الصيانة الجديدة
- تحديد نوع الصيانة (دورية، إصلاح، استبدال قطع غيار)
- تتبع حالة الصيانة (قيد الانتظار، قيد التنفيذ، مكتملة)
- تسجيل الفني المسؤول عن الصيانة

### 4. إدارة قطع الغيار
- تسجيل المخزون من قطع الغيار
- تتبع استهلاك قطع الغيار
- تنبيهات عند انخفاض المخزون
- إدارة الموردين وطلبات الشراء

### 5. إدارة الفواتير والمدفوعات
- إنشاء فواتير تفصيلية للخدمات وقطع الغيار
- تسجيل المدفوعات
- إصدار تقارير مالية

### 6. التقارير والإحصائيات
- تقارير يومية/أسبوعية/شهرية للصيانة
- تقارير الإيرادات والمصروفات
- تقارير أداء الفنيين
- تقارير قطع الغيار الأكثر استخداماً

## التقنيات المستخدمة

- **Frontend**: Next.js 14 مع TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Icons**: Lucide React
- **Forms**: React Hook Form مع Zod للتحقق
- **Charts**: Recharts للتقارير والإحصائيات

## متطلبات التشغيل

- Node.js 18 أو أحدث
- npm أو yarn أو pnpm
- حساب Supabase

## التثبيت والتشغيل

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من [الموقع الرسمي](https://nodejs.org/)

### 2. تثبيت المتطلبات
```bash
npm install
```

### 3. إعداد قاعدة البيانات

#### إنشاء مشروع Supabase جديد:
1. اذهب إلى [Supabase](https://supabase.com)
2. أنشئ حساب جديد أو سجل الدخول
3. أنشئ مشروع جديد
4. انسخ URL المشروع و API Key

#### تشغيل ملف قاعدة البيانات:
1. اذهب إلى SQL Editor في لوحة تحكم Supabase
2. انسخ محتوى ملف `database/schema.sql`
3. شغل الاستعلام لإنشاء الجداول والبيانات التجريبية

### 4. إعداد متغيرات البيئة
```bash
cp .env.local.example .env.local
```

ثم قم بتعديل ملف `.env.local` وأضف بيانات Supabase:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. تشغيل المشروع
```bash
npm run dev
```

سيعمل المشروع على `http://localhost:3000`

## هيكل المشروع

```
car-center/
├── src/
│   ├── app/                    # صفحات Next.js
│   │   ├── dashboard/          # لوحة التحكم
│   │   ├── customers/          # إدارة العملاء
│   │   ├── vehicles/           # إدارة السيارات
│   │   ├── maintenance/        # إدارة الصيانة
│   │   ├── inventory/          # إدارة المخزون
│   │   ├── invoices/           # إدارة الفواتير
│   │   └── reports/            # التقارير
│   ├── components/             # المكونات المشتركة
│   ├── lib/                    # المكتبات والأدوات
│   └── types/                  # تعريفات TypeScript
├── database/                   # ملفات قاعدة البيانات
└── public/                     # الملفات العامة
```

## الاستخدام

### 1. الصفحة الرئيسية (`/`)
- عرض نظرة عامة على النظام
- روابط سريعة للوحات الإدارة المختلفة
- معلومات عن الميزات الرئيسية

### 2. لوحة التحكم (`/dashboard`)
- إحصائيات سريعة عن المركز
- إجراءات سريعة للمهام الشائعة
- تنبيهات المخزون المنخفض
- نظرة عامة على الأداء

### 3. إدارة العملاء (`/customers`)
- إضافة عملاء جدد (`/customers/new`)
- البحث والتصفية في قائمة العملاء
- تعديل بيانات العملاء
- عرض تاريخ التعاملات
- ربط العملاء بسياراتهم

### 4. إدارة السيارات (`/vehicles`)
- تسجيل سيارات جديدة (`/vehicles/new`)
- ربط السيارات بالعملاء
- تحديث بيانات السيارات
- تتبع قراءات العداد
- عرض تاريخ الصيانة لكل سيارة

### 5. إدارة الصيانة (`/maintenance`)
- إنشاء طلبات صيانة جديدة (`/maintenance/new`)
- تتبع حالة الطلبات (معلقة، قيد التنفيذ، مكتملة)
- تعيين الفنيين للطلبات
- تسجيل الخدمات والقطع المستخدمة
- إدارة أولويات الطلبات

### 6. إدارة المخزون (`/inventory`)
- عرض قطع الغيار المتوفرة
- تتبع مستويات المخزون
- تنبيهات المخزون المنخفض
- إدارة الموردين
- تسجيل حركة المخزون

### 7. إدارة الفواتير (`/invoices`)
- إنشاء فواتير جديدة
- تتبع حالة الدفع
- إرسال الفواتير للعملاء
- تسجيل المدفوعات
- إدارة الفواتير المتأخرة

### 8. التقارير (`/reports`)
- تقارير الإيرادات الشهرية
- تقارير أداء الفنيين
- تقارير الخدمات الأكثر طلباً
- تقارير حالة المخزون
- إمكانية تصدير التقارير (PDF/Excel)

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الميزات المستقبلية

- [ ] تطبيق جوال للعملاء
- [ ] نظام إشعارات SMS/Email
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] نظام إدارة المواعيد
- [ ] تتبع GPS للسيارات
- [ ] نظام تقييم الخدمة
- [ ] إدارة الضمانات
- [ ] تكامل مع أنظمة المحاسبة
