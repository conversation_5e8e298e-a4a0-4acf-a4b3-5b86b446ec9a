# دليل التثبيت والتشغيل - نظام إدارة مركز صيانة السيارات

## المتطلبات الأساسية

### 1. تثبيت Node.js
- قم بتحميل Node.js الإصدار 18 أو أحدث من [nodejs.org](https://nodejs.org/)
- تأكد من التثبيت بتشغيل الأوامر التالية:
```bash
node --version
npm --version
```

### 2. إنشاء حساب Supabase
1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ حساب جديد أو سجل الدخول
3. أنشئ مشروع جديد
4. احفظ URL المشروع و API Keys

## خطوات التثبيت

### الخطوة 1: تحميل المشروع
```bash
# إذا كان لديك Git
git clone [repository-url]
cd car-center-management

# أو قم بتحميل الملفات وفك الضغط
```

### الخطوة 2: تثبيت المتطلبات
```bash
npm install
```

### الخطوة 3: إعداد قاعدة البيانات

#### أ. إعداد Supabase
1. اذهب إلى لوحة تحكم Supabase
2. اختر مشروعك
3. اذهب إلى "SQL Editor"
4. انسخ محتوى ملف `database/schema.sql`
5. الصق المحتوى في SQL Editor
6. اضغط "Run" لتنفيذ الاستعلام

#### ب. الحصول على بيانات الاتصال
1. اذهب إلى "Settings" > "API"
2. انسخ:
   - Project URL
   - anon/public key
   - service_role key (اختياري)

### الخطوة 4: إعداد متغيرات البيئة
```bash
# انسخ ملف المثال
cp .env.local.example .env.local

# عدل الملف وأضف بيانات Supabase
```

محتوى ملف `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

### الخطوة 5: تشغيل النظام
```bash
# تشغيل النظام في وضع التطوير
npm run dev

# أو تشغيل النظام في وضع الإنتاج
npm run build
npm start
```

## الوصول للنظام

بعد تشغيل النظام بنجاح، يمكنك الوصول إليه عبر:

### الروابط الرئيسية:
- **الصفحة الرئيسية:** http://localhost:3000
- **تسجيل الدخول:** http://localhost:3000/login
- **لوحة التحكم:** http://localhost:3000/dashboard

### بيانات تجريبية للدخول:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** 123456

## التحقق من التثبيت

### 1. فحص قاعدة البيانات
- اذهب إلى Supabase > Table Editor
- تأكد من وجود الجداول التالية:
  - customers
  - vehicles
  - technicians
  - services
  - parts
  - inventory
  - service_requests
  - service_items
  - invoices
  - payments

### 2. فحص البيانات التجريبية
- اذهب إلى جدول `customers`
- يجب أن ترى 3 عملاء تجريبيين
- اذهب إلى جدول `parts`
- يجب أن ترى 5 قطع غيار تجريبية

### 3. فحص الصفحات
قم بزيارة الصفحات التالية للتأكد من عملها:
- ✅ `/` - الصفحة الرئيسية
- ✅ `/login` - تسجيل الدخول
- ✅ `/dashboard` - لوحة التحكم
- ✅ `/customers` - إدارة العملاء
- ✅ `/vehicles` - إدارة السيارات
- ✅ `/maintenance` - إدارة الصيانة
- ✅ `/inventory` - إدارة المخزون
- ✅ `/invoices` - إدارة الفواتير
- ✅ `/reports` - التقارير
- ✅ `/settings` - الإعدادات

## حل المشاكل الشائعة

### مشكلة: خطأ في الاتصال بقاعدة البيانات
**الحل:**
1. تأكد من صحة بيانات `.env.local`
2. تأكد من تشغيل ملف `schema.sql` في Supabase
3. تأكد من أن المشروع في Supabase نشط

### مشكلة: الصفحات لا تظهر البيانات
**الحل:**
1. افتح Developer Tools في المتصفح
2. تحقق من وجود أخطاء في Console
3. تأكد من تشغيل البيانات التجريبية

### مشكلة: خطأ في تثبيت المتطلبات
**الحل:**
```bash
# امسح مجلد node_modules وأعد التثبيت
rm -rf node_modules
rm package-lock.json
npm install
```

### مشكلة: النظام لا يعمل على المنفذ 3000
**الحل:**
```bash
# استخدم منفذ مختلف
npm run dev -- -p 3001
```

## الخطوات التالية

بعد التثبيت الناجح، يمكنك:

### 1. إضافة بيانات حقيقية
- أضف عملاء حقيقيين من صفحة العملاء
- سجل سيارات العملاء
- أضف فنيين وخدمات

### 2. تخصيص النظام
- عدل معلومات المركز في الإعدادات
- أضف خدمات وقطع غيار جديدة
- خصص الألوان والشعار

### 3. إعداد النسخ الاحتياطي
- فعل النسخ الاحتياطي التلقائي في Supabase
- أنشئ نسخ احتياطية دورية

### 4. تطوير الميزات
- أضف نظام إشعارات SMS
- طور تطبيق جوال
- أضف تكامل مع أنظمة الدفع

## الدعم الفني

إذا واجهت أي مشاكل:
1. راجع هذا الدليل مرة أخرى
2. تحقق من ملف `README.md`
3. راجع ملف `FEATURES.md` للميزات المتاحة
4. افتح Issue في GitHub إذا كان المشروع متاح

## معلومات إضافية

### هيكل المشروع:
```
car-center/
├── src/
│   ├── app/                 # صفحات Next.js
│   ├── components/          # المكونات المشتركة
│   ├── lib/                 # المكتبات والأدوات
│   └── types/               # تعريفات TypeScript
├── database/                # ملفات قاعدة البيانات
├── public/                  # الملفات العامة
└── docs/                    # التوثيق
```

### التقنيات المستخدمة:
- **Frontend:** Next.js 14, TypeScript, Tailwind CSS
- **Backend:** Supabase (PostgreSQL)
- **Icons:** Lucide React
- **Charts:** Recharts

---

**مبروك! نظام إدارة مركز صيانة السيارات جاهز للاستخدام! 🎉**
