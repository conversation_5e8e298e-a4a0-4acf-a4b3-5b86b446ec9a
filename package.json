{"name": "car-center-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "clean": "rm -rf .next node_modules/.cache", "setup": "npm install && npm run build"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.4", "@supabase/auth-helpers-nextjs": "^0.8.7", "lucide-react": "^0.294.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "xlsx": "^0.18.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4"}}